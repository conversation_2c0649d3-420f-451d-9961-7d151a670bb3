// 智能追问系统
// 基于分析结果智能生成追问问题，深入了解用户情况

const { conversationManager } = require('./conversation-manager');

/**
 * 智能追问系统类
 * 负责分析用户问题和分析结果，生成个性化的追问问题
 */
class IntelligentInquiry {
  constructor() {
    this.questionPriority = {
      'high': ['timing', 'method', 'obstacle'],
      'medium': ['alternative', 'resources', 'environment'],
      'low': ['personality', 'background', 'preference']
    };
  }

  /**
   * 基于卦象和问题生成预分析询问（分析前收集信息）
   * @param {string} sessionId - 会话ID
   * @param {object} hexagramInfo - 卦象信息
   * @param {string} originalQuestion - 原始问题
   * @returns {array} 预分析询问问题列表
   */
  async generatePreAnalysisQuestions(sessionId, hexagramInfo, originalQuestion) {
    const session = conversationManager.getSession(sessionId);
    if (!session) return [];

    console.log(`🤖 为会话 ${sessionId} 生成预分析询问问题...`);

    // 分析问题类型和用户意图
    const questionAnalysis = this.analyzeUserQuestion(originalQuestion);

    // 分析卦象信息
    const hexagramAnalysis = this.analyzeHexagram(hexagramInfo, session.moduleType);

    // 基于知识库生成预分析问题
    const questions = await this.generateKnowledgeBasedQuestions(
      questionAnalysis,
      hexagramAnalysis,
      session.moduleType,
      session.context
    );

    // 按重要性排序
    const prioritizedQuestions = this.prioritizePreAnalysisQuestions(questions, questionAnalysis);

    console.log(`✅ 生成了 ${prioritizedQuestions.length} 个预分析询问问题`);
    return prioritizedQuestions;
  },

  /**
   * 分析用户问题类型和意图
   * @param {string} question - 用户问题
   * @returns {object} 问题分析结果
   */
  analyzeUserQuestion(question) {
    const analysis = {
      type: 'general',
      intent: [],
      keywords: [],
      urgency: 'medium',
      specificity: 'general'
    };

    // 问题类型识别
    const typePatterns = {
      'career': ['工作', '事业', '职业', '升职', '跳槽', '创业', '生意'],
      'finance': ['财运', '赚钱', '投资', '理财', '收入', '财富', '经济'],
      'relationship': ['感情', '恋爱', '婚姻', '配偶', '对象', '桃花', '分手'],
      'health': ['健康', '身体', '疾病', '病情', '康复', '养生'],
      'family': ['家庭', '父母', '子女', '家人', '亲情'],
      'education': ['学习', '考试', '学业', '升学', '读书'],
      'timing': ['什么时候', '何时', '时间', '日期', '期限']
    };

    for (const [type, keywords] of Object.entries(typePatterns)) {
      if (keywords.some(keyword => question.includes(keyword))) {
        analysis.type = type;
        analysis.keywords.push(...keywords.filter(k => question.includes(k)));
      }
    }

    // 意图识别
    const intentPatterns = {
      'prediction': ['会', '能', '是否', '会不会', '能不能'],
      'timing': ['什么时候', '何时', '多久', '期限'],
      'method': ['怎么', '如何', '怎样', '方法', '建议'],
      'choice': ['选择', '决定', '哪个', '还是', '或者']
    };

    for (const [intent, patterns] of Object.entries(intentPatterns)) {
      if (patterns.some(pattern => question.includes(pattern))) {
        analysis.intent.push(intent);
      }
    }

    // 紧急程度判断
    const urgencyKeywords = {
      'high': ['急', '紧急', '马上', '立即', '现在', '今天'],
      'low': ['将来', '以后', '未来', '长期', '慢慢']
    };

    for (const [level, keywords] of Object.entries(urgencyKeywords)) {
      if (keywords.some(keyword => question.includes(keyword))) {
        analysis.urgency = level;
        break;
      }
    }

    return analysis;
  },

  /**
   * 分析卦象信息
   * @param {object} hexagramInfo - 卦象信息
   * @param {string} moduleType - 模块类型
   * @returns {object} 卦象分析结果
   */
  analyzeHexagram(hexagramInfo, moduleType) {
    const analysis = {
      hexagramName: hexagramInfo.name || '',
      hexagramType: 'unknown',
      keyElements: [],
      traditionalMeaning: '',
      applicableScenarios: []
    };

    // 根据模块类型分析卦象
    switch (moduleType) {
      case 'yijing':
        analysis.keyElements = this.extractLiuyaoElements(hexagramInfo);
        analysis.traditionalMeaning = this.getLiuyaoTraditionalMeaning(hexagramInfo.name);
        break;
      case 'meihua':
        analysis.keyElements = this.extractMeihuaElements(hexagramInfo);
        analysis.traditionalMeaning = this.getMeihuaTraditionalMeaning(hexagramInfo.name);
        break;
    }

    return analysis;
  },

  /**
   * 提取六爻关键要素
   */
  extractLiuyaoElements(hexagramInfo) {
    const elements = [];

    if (hexagramInfo.changingYaos && hexagramInfo.changingYaos.length > 0) {
      elements.push(`动爻：第${hexagramInfo.changingYaos.join('、')}爻`);
    }

    if (hexagramInfo.liuyaoInfo) {
      if (hexagramInfo.liuyaoInfo.worldResponse) {
        elements.push(`世应：${hexagramInfo.liuyaoInfo.worldResponse}`);
      }
      if (hexagramInfo.liuyaoInfo.sixRelatives) {
        elements.push(`六亲：${hexagramInfo.liuyaoInfo.sixRelatives.join('、')}`);
      }
    }

    return elements;
  },

  /**
   * 提取梅花易数关键要素
   */
  extractMeihuaElements(hexagramInfo) {
    const elements = [];

    if (hexagramInfo.upper && hexagramInfo.lower) {
      elements.push(`体卦：${hexagramInfo.lower.name}`);
      elements.push(`用卦：${hexagramInfo.upper.name}`);
    }

    if (hexagramInfo.changingLine) {
      elements.push(`动爻：第${hexagramInfo.changingLine}爻`);
    }

    return elements;
  },

  /**
   * 获取六爻传统含义（基于知识库）
   */
  getLiuyaoTraditionalMeaning(hexagramName) {
    // 基于知识库的传统卦象含义
    const traditionalMeanings = {
      '乾为天': '刚健中正，君子自强不息，利于领导和创业',
      '坤为地': '柔顺承载，厚德载物，利于合作和辅助',
      '水雷屯': '初始困难，需要积累，不宜急进',
      '山水蒙': '启蒙教育，需要指导，宜求师问道',
      '水天需': '等待时机，需要耐心，不可强求',
      '天水讼': '争讼纠纷，需要谨慎，宜和解',
      '地水师': '统帅军队，需要纪律，宜团结',
      '水地比': '亲密合作，需要诚信，宜结盟'
    };

    return traditionalMeanings[hexagramName] || '需要根据具体情况分析';
  },

  /**
   * 获取梅花易数传统含义
   */
  getMeihuaTraditionalMeaning(hexagramName) {
    // 基于知识库的梅花易数含义
    const meihuaMeanings = {
      '乾为天': '天行健，君子以自强不息，主动积极',
      '坤为地': '地势坤，君子以厚德载物，柔顺包容',
      '震为雷': '雷声震动，主动变化，宜把握时机',
      '巽为风': '风行天下，渐进发展，宜顺势而为',
      '坎为水': '水流不息，险中求进，需要智慧',
      '离为火': '火光明亮，文明进步，宜展现才华',
      '艮为山': '山止不动，稳重踏实，宜守正待时',
      '兑为泽': '泽润万物，和悦交流，宜人际和谐'
    };

    return meihuaMeanings[hexagramName] || '需要结合体用关系分析';
  },

  /**
   * 基于知识库生成预分析问题
   * @param {object} questionAnalysis - 问题分析
   * @param {object} hexagramAnalysis - 卦象分析
   * @param {string} moduleType - 模块类型
   * @param {object} context - 会话上下文
   * @returns {array} 基于知识库的问题列表
   */
  async generateKnowledgeBasedQuestions(questionAnalysis, hexagramAnalysis, moduleType, context) {
    const questions = [];

    // 基于问题类型生成知识库相关问题
    const typeBasedQuestions = this.getKnowledgeBasedTypeQuestions(questionAnalysis.type, moduleType);
    questions.push(...typeBasedQuestions);

    // 基于卦象特性生成问题
    const hexagramBasedQuestions = this.getHexagramBasedQuestions(hexagramAnalysis, questionAnalysis.type);
    questions.push(...hexagramBasedQuestions);

    // 基于传统理论生成问题
    const theoryBasedQuestions = this.getTheoryBasedQuestions(moduleType, questionAnalysis);
    questions.push(...theoryBasedQuestions);

    return questions.slice(0, 4); // 限制最多4个问题
  },

  /**
   * 分析占卜结果中的关键信息
   * @param {object} result - 分析结果
   * @param {string} moduleType - 模块类型
   * @returns {object} 结果分析
   */
  analyzeResult(result, moduleType) {
    const analysis = {
      sentiment: 'neutral', // positive, negative, neutral
      confidence: 'medium',
      keyPoints: [],
      suggestions: [],
      warnings: [],
      opportunities: []
    };

    const resultText = result.aiAnalysis || result.analysis || '';

    // 情感倾向分析
    const positiveWords = ['吉', '好', '顺利', '成功', '有利', '旺', '佳', '利'];
    const negativeWords = ['凶', '不利', '困难', '阻碍', '破', '冲', '害', '忌'];

    const positiveCount = positiveWords.filter(word => resultText.includes(word)).length;
    const negativeCount = negativeWords.filter(word => resultText.includes(word)).length;

    if (positiveCount > negativeCount) {
      analysis.sentiment = 'positive';
    } else if (negativeCount > positiveCount) {
      analysis.sentiment = 'negative';
    }

    // 提取关键建议
    const suggestionPatterns = ['建议', '应该', '可以', '不宜', '适合', '避免'];
    suggestionPatterns.forEach(pattern => {
      const regex = new RegExp(`${pattern}[^。！？]*[。！？]`, 'g');
      const matches = resultText.match(regex);
      if (matches) {
        analysis.suggestions.push(...matches);
      }
    });

    // 提取时间信息
    const timePatterns = ['月', '年', '日', '时间', '期间', '阶段'];
    timePatterns.forEach(pattern => {
      if (resultText.includes(pattern)) {
        analysis.keyPoints.push(`包含时间预测：${pattern}`);
      }
    });

    return analysis;
  }

  /**
   * 基于知识库生成问题类型相关问题
   */
  getKnowledgeBasedTypeQuestions(questionType, moduleType) {
    const knowledgeBasedQuestions = {
      yijing: {
        career: [
          { text: '您目前从事什么行业？这有助于分析官鬼爻的具体含义。', priority: 'high', knowledge: '官鬼爻代表工作事业，不同行业有不同解读' },
          { text: '您是想升职、跳槽，还是创业？不同目标对应不同的六亲分析。', priority: 'high', knowledge: '升职看官鬼，跳槽看世应，创业看财爻' },
          { text: '您的年龄段是？青年、中年、老年在六爻中有不同的判断标准。', priority: 'medium', knowledge: '年龄影响大运流年的解读' }
        ],
        finance: [
          { text: '您是想了解正财（工资收入）还是偏财（投资收益）？', priority: 'high', knowledge: '妻财爻分正财偏财，解读方法不同' },
          { text: '您的投资类型是什么？股票、房产、生意？不同类型对应不同的五行分析。', priority: 'high', knowledge: '五行生克决定投资成败' },
          { text: '您希望了解短期（1年内）还是长期（3-5年）的财运？', priority: 'medium', knowledge: '时间长短影响动静爻的解读' }
        ],
        relationship: [
          { text: '您是男性还是女性？这决定了如何看配偶宫。', priority: 'high', knowledge: '男看妻财女看官鬼，性别决定六亲取用' },
          { text: '您目前是单身、恋爱中还是已婚？不同状态的分析重点不同。', priority: 'high', knowledge: '单身看桃花，恋爱看合冲，已婚看刑害' },
          { text: '您的年龄段？不同年龄的感情问题有不同的解读方式。', priority: 'medium', knowledge: '年龄影响感情运势的判断' }
        ]
      },
      meihua: {
        career: [
          { text: '您希望了解事业的哪个方面？发展方向、时机选择还是人际关系？', priority: 'high', knowledge: '体用生克决定事业成败' },
          { text: '您的性格偏向主动还是被动？这影响体用关系的解读。', priority: 'medium', knowledge: '性格与卦象相应，影响成事概率' }
        ],
        finance: [
          { text: '您的理财风格是稳健还是激进？这与五行属性相关。', priority: 'high', knowledge: '五行属性决定适合的投资方式' },
          { text: '您最关心的是投资时机还是投资方向？', priority: 'high', knowledge: '时机看动爻，方向看体用' }
        ]
      }
    };

    const moduleQuestions = knowledgeBasedQuestions[moduleType] || {};
    return moduleQuestions[questionType] || [];
  },

  /**
   * 基于卦象特性生成问题
   */
  getHexagramBasedQuestions(hexagramAnalysis, questionType) {
    const questions = [];

    // 根据卦象名称生成特定问题
    const hexagramName = hexagramAnalysis.hexagramName;

    if (hexagramName.includes('乾')) {
      questions.push({
        text: '乾卦主刚健，您是否准备主动出击？还是希望稳妥行事？',
        priority: 'high',
        knowledge: '乾卦刚健，利于主动，但需要把握分寸'
      });
    } else if (hexagramName.includes('坤')) {
      questions.push({
        text: '坤卦主柔顺，您是否愿意采取配合、辅助的角色？',
        priority: 'high',
        knowledge: '坤卦柔顺，利于配合，不宜强出头'
      });
    } else if (hexagramName.includes('屯')) {
      questions.push({
        text: '屯卦主困难，您目前遇到的最大阻碍是什么？',
        priority: 'high',
        knowledge: '屯卦初难，需要了解具体困难才能给出建议'
      });
    }

    return questions;
  },

  /**
   * 基于传统理论生成问题
   */
  getTheoryBasedQuestions(moduleType, questionAnalysis) {
    const theoryQuestions = {
      yijing: [
        { text: '您希望了解具体的时间节点吗？比如几月份比较有利？', priority: 'medium', knowledge: '六爻可以预测具体时间，需要结合月令分析' },
        { text: '您有什么特别需要避免的事情吗？', priority: 'medium', knowledge: '忌神所在，需要特别注意' }
      ],
      meihua: [
        { text: '您做这件事的环境如何？是在家里、办公室还是其他地方？', priority: 'medium', knowledge: '梅花易数重视环境因素，影响卦象解读' },
        { text: '您的直觉告诉您这件事会如何发展？', priority: 'low', knowledge: '梅花易数重视直觉感应' }
      ]
    };

    return theoryQuestions[moduleType] || [];
  },

  /**
   * 预分析问题优先级排序
   */
  prioritizePreAnalysisQuestions(questions, questionAnalysis) {
    const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 };

    return questions.sort((a, b) => {
      const aPriority = priorityOrder[a.priority] || 1;
      const bPriority = priorityOrder[b.priority] || 1;
      return bPriority - aPriority;
    });
  },

  /**
   * 生成个性化追问问题
   * @param {object} questionAnalysis - 问题分析
   * @param {object} resultAnalysis - 结果分析
   * @param {string} moduleType - 模块类型
   * @param {object} context - 会话上下文
   * @returns {array} 个性化问题列表
   */
  async generatePersonalizedQuestions(questionAnalysis, resultAnalysis, moduleType, context) {
    const questions = [];

    // 基于问题类型生成追问
    const typeBasedQuestions = this.getTypeBasedQuestions(questionAnalysis.type, moduleType);
    questions.push(...typeBasedQuestions);

    // 基于分析结果生成追问
    const resultBasedQuestions = this.getResultBasedQuestions(resultAnalysis, questionAnalysis);
    questions.push(...resultBasedQuestions);

    // 基于模块特性生成追问
    const moduleBasedQuestions = this.getModuleBasedQuestions(moduleType, questionAnalysis);
    questions.push(...moduleBasedQuestions);

    // 去重和个性化调整
    const uniqueQuestions = this.deduplicateQuestions(questions);
    const personalizedQuestions = this.personalizeQuestions(uniqueQuestions, context);

    return personalizedQuestions.slice(0, 5); // 限制最多5个问题
  }

  /**
   * 基于问题类型获取追问问题
   */
  getTypeBasedQuestions(type, moduleType) {
    const questionBank = {
      career: [
        { text: '您目前的工作状况如何？是否遇到了具体的困难？', priority: 'high', category: 'current_status' },
        { text: '您理想的职业发展方向是什么？', priority: 'medium', category: 'goal' },
        { text: '您在工作中最看重什么？薪资、发展空间还是工作环境？', priority: 'medium', category: 'preference' }
      ],
      finance: [
        { text: '您目前的财务状况如何？有什么具体的理财目标吗？', priority: 'high', category: 'current_status' },
        { text: '您的风险承受能力如何？偏好稳健还是激进的投资方式？', priority: 'high', category: 'preference' },
        { text: '您希望在多长时间内实现财务目标？', priority: 'medium', category: 'timeline' }
      ],
      relationship: [
        { text: '您目前的感情状况如何？单身还是有伴侣？', priority: 'high', category: 'current_status' },
        { text: '您理想的伴侣类型是什么样的？', priority: 'medium', category: 'preference' },
        { text: '您在感情中遇到的主要困扰是什么？', priority: 'high', category: 'obstacle' }
      ],
      health: [
        { text: '您目前的身体状况如何？有什么不适症状吗？', priority: 'high', category: 'current_status' },
        { text: '您的生活作息和饮食习惯怎样？', priority: 'medium', category: 'lifestyle' },
        { text: '您有定期体检或运动的习惯吗？', priority: 'low', category: 'habit' }
      ]
    };

    return questionBank[type] || [];
  }

  /**
   * 基于分析结果获取追问问题
   */
  getResultBasedQuestions(resultAnalysis, questionAnalysis) {
    const questions = [];

    // 基于情感倾向
    if (resultAnalysis.sentiment === 'negative') {
      questions.push({
        text: '看起来情况比较复杂，您最担心的是什么方面？',
        priority: 'high',
        category: 'concern'
      });
    } else if (resultAnalysis.sentiment === 'positive') {
      questions.push({
        text: '机会很好！您准备如何把握这个机会？',
        priority: 'medium',
        category: 'action'
      });
    }

    // 基于建议内容
    if (resultAnalysis.suggestions.length > 0) {
      questions.push({
        text: '关于刚才的建议，您觉得哪一点最适合您的情况？',
        priority: 'high',
        category: 'feedback'
      });
    }

    return questions;
  }

  /**
   * 基于模块特性获取追问问题
   */
  getModuleBasedQuestions(moduleType, questionAnalysis) {
    const moduleQuestions = {
      yijing: [
        { text: '您希望了解更具体的时间节点吗？', priority: 'high', category: 'timing' },
        { text: '您想知道应该采取什么具体行动吗？', priority: 'high', category: 'method' }
      ],
      meihua: [
        { text: '您当前的环境和条件如何？', priority: 'medium', category: 'environment' },
        { text: '您的性格特点是什么？这有助于给出更适合的建议。', priority: 'low', category: 'personality' }
      ],
      bazi: [
        { text: '您想了解未来几年的运势变化吗？', priority: 'high', category: 'fortune' },
        { text: '您最关心人生的哪个方面？事业、财运还是感情？', priority: 'medium', category: 'focus' }
      ],
      ziwei: [
        { text: '您的人生目标和理想是什么？', priority: 'medium', category: 'goal' },
        { text: '您最希望在哪个方面得到发展和提升？', priority: 'medium', category: 'development' }
      ]
    };

    return moduleQuestions[moduleType] || [];
  }

  /**
   * 问题去重
   */
  deduplicateQuestions(questions) {
    const seen = new Set();
    return questions.filter(q => {
      const key = q.text.substring(0, 20); // 使用前20个字符作为去重标识
      if (seen.has(key)) return false;
      seen.add(key);
      return true;
    });
  }

  /**
   * 个性化问题调整
   */
  personalizeQuestions(questions, context) {
    // 根据用户历史和偏好调整问题
    return questions.map(q => ({
      ...q,
      id: `q_${Date.now()}_${Math.random().toString(36).substring(2, 6)}`,
      timestamp: new Date()
    }));
  }

  /**
   * 问题优先级排序
   */
  prioritizeQuestions(questions, questionAnalysis) {
    const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 };
    
    return questions.sort((a, b) => {
      const aPriority = priorityOrder[a.priority] || 1;
      const bPriority = priorityOrder[b.priority] || 1;
      return bPriority - aPriority;
    });
  }

  /**
   * 生成AI驱动的深度追问
   * @param {string} sessionId - 会话ID
   * @param {string} userResponse - 用户回答
   * @returns {object} AI生成的追问
   */
  async generateAIFollowUp(sessionId, userResponse) {
    const session = conversationManager.getSession(sessionId);
    if (!session) return null;

    try {
      // 构建AI追问提示词
      const prompt = this.buildFollowUpPrompt(session, userResponse);
      
      // 调用DeepSeek API生成追问
      const aiResponse = await this.callDeepSeekForFollowUp(prompt);
      
      if (aiResponse.success) {
        return {
          type: 'ai_generated',
          text: aiResponse.question,
          context: aiResponse.context,
          priority: 'high'
        };
      }
      
    } catch (error) {
      console.error('AI追问生成失败:', error);
    }
    
    return null;
  }

  /**
   * 构建AI追问提示词
   */
  buildFollowUpPrompt(session, userResponse) {
    const { moduleType, context, messageHistory } = session;
    const lastAnalysis = context.analysisResults[context.analysisResults.length - 1];
    
    return `你是专业的${this.getModuleName(moduleType)}大师，正在与用户进行深入的占卜咨询。

【对话历史】
${messageHistory.slice(-3).map(m => `${m.role}: ${m.content}`).join('\n')}

【最新分析结果】
${lastAnalysis ? lastAnalysis.result.aiAnalysis : '暂无'}

【用户最新回答】
${userResponse}

请基于以上信息，生成一个深入的追问问题，要求：
1. 针对用户的具体情况
2. 有助于提供更精准的指导
3. 体现专业的${this.getModuleName(moduleType)}理论
4. 语气亲切专业，像面对面咨询

只返回追问问题，不要其他内容。`;
  }

  /**
   * 调用DeepSeek API生成追问
   */
  async callDeepSeekForFollowUp(prompt) {
    return new Promise((resolve) => {
      wx.request({
        url: 'https://api.deepseek.com/v1/chat/completions',
        method: 'POST',
        header: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer sk-********************************'
        },
        data: {
          model: 'deepseek-chat',
          messages: [{ role: 'user', content: prompt }],
          temperature: 0.3,
          max_tokens: 200
        },
        success: (response) => {
          if (response.statusCode === 200 && response.data?.choices?.[0]) {
            resolve({
              success: true,
              question: response.data.choices[0].message.content.trim()
            });
          } else {
            resolve({ success: false, error: 'API响应异常' });
          }
        },
        fail: (error) => {
          resolve({ success: false, error: error.errMsg });
        }
      });
    });
  }

  /**
   * 获取模块中文名称
   */
  getModuleName(moduleType) {
    const names = {
      'yijing': '六爻',
      'meihua': '梅花易数',
      'bazi': '八字',
      'ziwei': '紫微斗数'
    };
    return names[moduleType] || '占卜';
  }
}

// 创建全局实例
const intelligentInquiry = new IntelligentInquiry();

module.exports = {
  IntelligentInquiry,
  intelligentInquiry
};
